const https = require('https');
const querystring = require('querystring');

// Service configuration
const SERVICE_URL = 'https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor/submitJob';
const BASE_URL = 'https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor';
const TOKEN_URL = 'https://nonprod-arcgis.purolator.com/portal/sharing/rest/generateToken';

// Authentication configuration (you'll need to provide these)
const AUTH_CONFIG = {
  username: process.env.ARCGIS_USERNAME || '',
  password: process.env.ARCGIS_PASSWORD || '',
  // Alternative: use existing token if you have one
  token: process.env.ARCGIS_TOKEN || ''
};

// Test data
const testParams = {
  json_input: JSON.stringify({
    territoryPlanName: "STP67890-V5-012",
    terminalId: "12",
    shipments: [
      {
        id: "SH001",
        latitude: 43.603278,
        longitude: -79.512321
      },
      {
        id: "SH002",
        latitude: 43.591175,
        longitude: -79.640464,
        address3: "2-401"
      }
    ]
  }),
  use_business_point_search: true,
  f: 'json'
};

// Helper function to make HTTP requests
function makeRequest(url, method = 'GET', postData = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (postData && method === 'POST') {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (postData && method === 'POST') {
      req.write(postData);
    }
    req.end();
  });
}

// Function to generate authentication token
async function generateToken() {
  if (AUTH_CONFIG.token) {
    console.log('🔑 Using provided token...');
    return AUTH_CONFIG.token;
  }

  if (!AUTH_CONFIG.username || !AUTH_CONFIG.password) {
    console.log('❌ No authentication credentials provided');
    console.log('Please set environment variables:');
    console.log('  ARCGIS_USERNAME=your_username');
    console.log('  ARCGIS_PASSWORD=your_password');
    console.log('  OR');
    console.log('  ARCGIS_TOKEN=your_existing_token');
    return null;
  }

  console.log('🔑 Generating authentication token...');
  try {
    const tokenParams = {
      username: AUTH_CONFIG.username,
      password: AUTH_CONFIG.password,
      client: 'requestip',
      f: 'json'
    };

    const postData = querystring.stringify(tokenParams);
    const response = await makeRequest(TOKEN_URL, 'POST', postData);
    
    console.log('Token Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data && response.data.token) {
      console.log('✅ Token generated successfully');
      return response.data.token;
    } else {
      console.error('❌ Failed to generate token');
      return null;
    }
  } catch (error) {
    console.error('Error generating token:', error.message);
    return null;
  }
}

// Function to check service info with token
async function checkServiceInfo(token) {
  console.log('🔍 Checking service information...');
  try {
    const url = `${BASE_URL}?f=json${token ? `&token=${token}` : ''}`;
    const response = await makeRequest(url);
    console.log('Service Info Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error checking service info:', error.message);
    return null;
  }
}

// Function to submit job with token
async function submitJob(token) {
  console.log('🚀 Submitting job to service...');
  try {
    const params = { ...testParams };
    if (token) {
      params.token = token;
    }
    
    const postData = querystring.stringify(params);
    const response = await makeRequest(SERVICE_URL, 'POST', postData);
    
    console.log('Submit Job Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data && response.data.jobId) {
      return response.data.jobId;
    } else {
      console.error('No job ID returned');
      return null;
    }
  } catch (error) {
    console.error('Error submitting job:', error.message);
    return null;
  }
}

// Function to check job status with token
async function checkJobStatus(jobId, token) {
  console.log(`📊 Checking status for job: ${jobId}`);
  try {
    const statusUrl = `${BASE_URL}/jobs/${jobId}?f=json${token ? `&token=${token}` : ''}`;
    const response = await makeRequest(statusUrl);
    console.log('Job Status Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error checking job status:', error.message);
    return null;
  }
}

// Function to get job results with token
async function getJobResults(jobId, token) {
  console.log(`📋 Getting results for job: ${jobId}`);
  try {
    const resultsUrl = `${BASE_URL}/jobs/${jobId}/results/output_results?f=json${token ? `&token=${token}` : ''}`;
    const response = await makeRequest(resultsUrl);
    console.log('Job Results Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting job results:', error.message);
    return null;
  }
}

// Function to wait for job completion
async function waitForJobCompletion(jobId, token, maxWaitTime = 60000, interval = 2000) {
  console.log(`⏳ Waiting for job completion (max ${maxWaitTime/1000}s)...`);
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const status = await checkJobStatus(jobId, token);
    
    if (!status) {
      console.error('Failed to get job status');
      return false;
    }
    
    console.log(`Job status: ${status.jobStatus}`);
    
    if (status.jobStatus === 'esriJobSucceeded') {
      console.log('✅ Job completed successfully!');
      return true;
    } else if (status.jobStatus === 'esriJobFailed') {
      console.log('❌ Job failed!');
      if (status.messages) {
        console.log('Error messages:', status.messages);
      }
      return false;
    } else if (status.jobStatus === 'esriJobCancelled') {
      console.log('🚫 Job was cancelled!');
      return false;
    }
    
    // Wait before checking again
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  console.log('⏰ Job did not complete within the timeout period');
  return false;
}

// Main test function
async function runTest() {
  console.log('🧪 Starting authenticated service test...\n');
  
  // Step 1: Get authentication token
  const token = await generateToken();
  if (!token) {
    console.log('❌ Cannot proceed without authentication token');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 2: Check service info
  const serviceInfo = await checkServiceInfo(token);
  if (!serviceInfo || serviceInfo.error) {
    console.log('❌ Could not retrieve service information');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 3: Submit job
  const jobId = await submitJob(token);
  if (!jobId) {
    console.log('❌ Failed to submit job');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 4: Wait for completion
  const completed = await waitForJobCompletion(jobId, token);
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 5: Get results if successful
  if (completed) {
    await getJobResults(jobId, token);
  }
  
  console.log('\n🏁 Test completed!');
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = {
  generateToken,
  checkServiceInfo,
  submitJob,
  checkJobStatus,
  getJobResults,
  waitForJobCompletion,
  runTest
};
