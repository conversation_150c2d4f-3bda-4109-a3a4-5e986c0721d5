try {
  // Define the service URL
  const serviceUrl = "https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor/submitJob";

  // Prepare input parameters for the geoprocessing service
  const params = {
    json_input: JSON.stringify({
      territoryPlanName: "STP67890-V5-012",
      terminalId: "12",
      shipments: [
        {
          id: "SH001",
          latitude: 43.603278,
          longitude: -79.512321
        },
        {
          id: "SH002",
          latitude: 43.591175,
          longitude: -79.640464,
          address3: "2-401"
        }
      ]
    }),
    use_business_point_search: true
  };

  // Set up job monitoring options
  const options = {
    interval: 1500, // Check job status every 1.5 seconds
    statusCallback: (job) => {
      console.log("Job Status: ", job.jobStatus);
    }
  };

  // Submit the job to the geoprocessing service
  const jobInfo = await geoprocessor.submitJob(serviceUrl, params);
  
  // Wait for the job to complete
  await jobInfo.waitForJobCompletion(options);
  
  // Check the job status
  if (jobInfo.jobStatus === "job-succeeded") {
    // Get the results from the completed job
    const results = await jobInfo.fetchResultData("output_results");
    console.log("Results:", results);
    
    // Process the batch shipment results
    if (results && results.value) {
      const batchResults = results.value;
      console.log(`Territory Plan: ${batchResults.territoryPlanName}`);
      console.log(`Terminal ID: ${batchResults.terminalId}`);
      console.log(`Processed ${batchResults.processedCount} shipments`);
      
      // Process individual shipment results
      batchResults.shipmentResults.forEach(shipment => {
        console.log(`Shipment ${shipment.ShipmentId}: Route ${shipment.RouteID}, Segment ${shipment.SegmentID}`);
        if (shipment.BusinessPointMatch) {
          console.log(`  Matched to business: ${shipment.BusinessName}, Unit: ${shipment.UnitNumber}`);
        }
      });
    }
  } else {
    // Handle job failure
    console.error(`Job failed with status: ${jobInfo.jobStatus}`);
    
    // Optionally fetch detailed error messages
    const messages = await jobInfo.fetchMessages();
    const errorMessages = messages
      .filter(msg => msg.type === "error")
      .map(msg => msg.description);
    
    console.error("Error details:", errorMessages);
  }
  
} catch (error) {
  console.error("Error submitting geoprocessing job:", error);
}