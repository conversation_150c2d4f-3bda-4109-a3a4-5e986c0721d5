const https = require('https');
const querystring = require('querystring');

// Service configuration
const SERVICE_URL = 'https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor/submitJob';
const BASE_URL = 'https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor';

// Test data
const testParams = {
  json_input: JSON.stringify({
    territoryPlanName: "STP67890-V5-012",
    terminalId: "12",
    shipments: [
      {
        id: "SH001",
        latitude: 43.603278,
        longitude: -79.512321
      },
      {
        id: "SH002",
        latitude: 43.591175,
        longitude: -79.640464,
        address3: "2-401"
      }
    ]
  }),
  use_business_point_search: true,
  f: 'json'
};

// Helper function to make HTTP requests
function makeRequest(url, method = 'GET', postData = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Node.js Test Client'
      }
    };

    if (postData && method === 'POST') {
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (postData && method === 'POST') {
      req.write(postData);
    }
    req.end();
  });
}

// Function to check service info
async function checkServiceInfo() {
  console.log('🔍 Checking service information...');
  try {
    const response = await makeRequest(`${BASE_URL}?f=json`);
    console.log('Service Info Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error checking service info:', error.message);
    return null;
  }
}

// Function to submit job
async function submitJob() {
  console.log('🚀 Submitting job to service...');
  try {
    const postData = querystring.stringify(testParams);
    const response = await makeRequest(SERVICE_URL, 'POST', postData);
    
    console.log('Submit Job Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data && response.data.jobId) {
      return response.data.jobId;
    } else {
      console.error('No job ID returned');
      return null;
    }
  } catch (error) {
    console.error('Error submitting job:', error.message);
    return null;
  }
}

// Function to check job status
async function checkJobStatus(jobId) {
  console.log(`📊 Checking status for job: ${jobId}`);
  try {
    const statusUrl = `${BASE_URL}/jobs/${jobId}?f=json`;
    const response = await makeRequest(statusUrl);
    console.log('Job Status Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error checking job status:', error.message);
    return null;
  }
}

// Function to get job results
async function getJobResults(jobId) {
  console.log(`📋 Getting results for job: ${jobId}`);
  try {
    const resultsUrl = `${BASE_URL}/jobs/${jobId}/results/output_results?f=json`;
    const response = await makeRequest(resultsUrl);
    console.log('Job Results Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting job results:', error.message);
    return null;
  }
}

// Function to wait for job completion
async function waitForJobCompletion(jobId, maxWaitTime = 60000, interval = 2000) {
  console.log(`⏳ Waiting for job completion (max ${maxWaitTime/1000}s)...`);
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const status = await checkJobStatus(jobId);
    
    if (!status) {
      console.error('Failed to get job status');
      return false;
    }
    
    console.log(`Job status: ${status.jobStatus}`);
    
    if (status.jobStatus === 'esriJobSucceeded') {
      console.log('✅ Job completed successfully!');
      return true;
    } else if (status.jobStatus === 'esriJobFailed') {
      console.log('❌ Job failed!');
      if (status.messages) {
        console.log('Error messages:', status.messages);
      }
      return false;
    } else if (status.jobStatus === 'esriJobCancelled') {
      console.log('🚫 Job was cancelled!');
      return false;
    }
    
    // Wait before checking again
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  console.log('⏰ Job did not complete within the timeout period');
  return false;
}

// Main test function
async function runTest() {
  console.log('🧪 Starting service test...\n');
  
  // Step 1: Check service info
  const serviceInfo = await checkServiceInfo();
  if (!serviceInfo) {
    console.log('❌ Could not retrieve service information');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 2: Submit job
  const jobId = await submitJob();
  if (!jobId) {
    console.log('❌ Failed to submit job');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 3: Wait for completion
  const completed = await waitForJobCompletion(jobId);
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Step 4: Get results if successful
  if (completed) {
    await getJobResults(jobId);
  }
  
  console.log('\n🏁 Test completed!');
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = {
  checkServiceInfo,
  submitJob,
  checkJobStatus,
  getJobResults,
  waitForJobCompletion,
  runTest
};
