const readline = require('readline');
const { generateToken, checkServiceInfo, submitJob, waitForJobCompletion, getJobResults } = require('./test_service_with_auth');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function interactiveTest() {
  console.log('🧪 Interactive ArcGIS Service Test');
  console.log('==================================\n');
  
  console.log('This script will help you test the Purolator ArcGIS service.');
  console.log('You can either:');
  console.log('1. Provide username/password for authentication');
  console.log('2. Provide an existing token');
  console.log('3. Test without authentication (will likely fail)\n');
  
  const authChoice = await question('Choose authentication method (1/2/3): ');
  
  let token = null;
  
  if (authChoice === '1') {
    const username = await question('Enter username: ');
    const password = await question('Enter password: ');
    
    // Set environment variables temporarily
    process.env.ARCGIS_USERNAME = username;
    process.env.ARCGIS_PASSWORD = password;
    
    console.log('\n🔑 Attempting to generate token...');
    token = await generateToken();
    
    if (!token) {
      console.log('❌ Failed to generate token. Exiting.');
      rl.close();
      return;
    }
    
  } else if (authChoice === '2') {
    token = await question('Enter your token: ');
    
  } else {
    console.log('\n⚠️  Proceeding without authentication (will likely fail)...');
  }
  
  console.log('\n' + '='.repeat(50));
  
  // Test service info
  console.log('\n📋 Testing service information...');
  const serviceInfo = await checkServiceInfo(token);
  
  if (serviceInfo && !serviceInfo.error) {
    console.log('✅ Service info retrieved successfully!');
    
    const continueTest = await question('\nDo you want to submit a test job? (y/n): ');
    
    if (continueTest.toLowerCase() === 'y') {
      console.log('\n🚀 Submitting test job...');
      const jobId = await submitJob(token);
      
      if (jobId) {
        console.log(`✅ Job submitted successfully! Job ID: ${jobId}`);
        
        const waitForCompletion = await question('Wait for job completion? (y/n): ');
        
        if (waitForCompletion.toLowerCase() === 'y') {
          console.log('\n⏳ Waiting for job completion...');
          const completed = await waitForJobCompletion(jobId, token);
          
          if (completed) {
            console.log('\n📋 Getting job results...');
            await getJobResults(jobId, token);
          }
        } else {
          console.log(`\n💡 You can check job status later with job ID: ${jobId}`);
        }
      }
    }
  } else {
    console.log('❌ Failed to retrieve service info');
    if (serviceInfo && serviceInfo.error) {
      console.log(`Error: ${serviceInfo.error.message} (Code: ${serviceInfo.error.code})`);
    }
  }
  
  console.log('\n🏁 Test completed!');
  rl.close();
}

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n\n👋 Test interrupted by user');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  interactiveTest().catch((error) => {
    console.error('Error during test:', error);
    rl.close();
  });
}
