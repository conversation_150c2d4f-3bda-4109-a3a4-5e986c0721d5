#!/bin/bash

# Service URLs
BASE_URL="https://nonprod-arcgis.purolator.com/server/rest/services/SRM/Route_Shelf_Instructions/GPServer/Batch%20Shipment%20Processor"
SUBMIT_URL="${BASE_URL}/submitJob"
TOKEN_URL="https://nonprod-arcgis.purolator.com/server/rest/services/generateToken"

echo "🧪 Testing Purolator ArcGIS Service with curl"
echo "=============================================="

# Test 1: Check service info without authentication
echo ""
echo "📋 Test 1: Service Info (no auth)"
echo "URL: ${BASE_URL}?f=json"
curl -s "${BASE_URL}?f=json" | jq '.' 2>/dev/null || curl -s "${BASE_URL}?f=json"

echo ""
echo "=============================================="

# Test 2: Try to submit job without authentication
echo ""
echo "🚀 Test 2: Submit Job (no auth)"
echo "URL: ${SUBMIT_URL}"

# Prepare test data
TEST_DATA=$(cat << 'EOF'
{
  "territoryPlanName": "STP67890-V5-012",
  "terminalId": "12",
  "shipments": [
    {
      "id": "SH001",
      "latitude": 43.603278,
      "longitude": -79.512321
    },
    {
      "id": "SH002",
      "latitude": 43.591175,
      "longitude": -79.640464,
      "address3": "2-401"
    }
  ]
}
EOF
)

curl -s -X POST "${SUBMIT_URL}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "json_input=${TEST_DATA}" \
  -d "use_business_point_search=true" \
  -d "f=json" | jq '.' 2>/dev/null || curl -s -X POST "${SUBMIT_URL}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "json_input=${TEST_DATA}" \
  -d "use_business_point_search=true" \
  -d "f=json"

echo ""
echo "=============================================="

# Test 3: Check if we can access the token generation endpoint
echo ""
echo "🔑 Test 3: Token Generation Endpoint"
echo "URL: ${TOKEN_URL}"
curl -s -X POST "${TOKEN_URL}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "f=json" | jq '.' 2>/dev/null || curl -s -X POST "${TOKEN_URL}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "f=json"

echo ""
echo "=============================================="

# Test 4: Check server info
echo ""
echo "🖥️  Test 4: Server Info"
SERVER_INFO_URL="https://nonprod-arcgis.purolator.com/server/rest/info?f=json"
echo "URL: ${SERVER_INFO_URL}"
curl -s "${SERVER_INFO_URL}" | jq '.' 2>/dev/null || curl -s "${SERVER_INFO_URL}"

echo ""
echo "=============================================="

# Test 5: Check services directory
echo ""
echo "📁 Test 5: Services Directory"
SERVICES_URL="https://nonprod-arcgis.purolator.com/server/rest/services?f=json"
echo "URL: ${SERVICES_URL}"
curl -s "${SERVICES_URL}" | jq '.' 2>/dev/null || curl -s "${SERVICES_URL}"

echo ""
echo "=============================================="
echo "🏁 Tests completed!"
echo ""
echo "💡 Next steps:"
echo "   1. If you have credentials, set them as environment variables:"
echo "      export ARCGIS_USERNAME='your_username'"
echo "      export ARCGIS_PASSWORD='your_password'"
echo "   2. Then run: node test_service_with_auth.js"
echo "   3. Or if you have a token: export ARCGIS_TOKEN='your_token'"
